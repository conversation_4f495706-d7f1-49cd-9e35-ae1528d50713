#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口序号重置工具
重置 dev-api-guidelines-pyapi.mdc 文件中的API接口序号，从1开始重新编号
"""

import re
import sys
import os

def reset_api_numbers(file_path):
    """
    重置API接口序号
    
    Args:
        file_path (str): 文件路径
    """
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 正则表达式匹配API接口行
        # 匹配格式：- [ ] **数字** 接口名称 `HTTP方法 /路径`
        api_pattern = r'- \[ \] \*\*(\d+)\*\* (.+?) `(GET|POST|PUT|DELETE|PATCH) (.+?)`'
        
        # 找到所有匹配的API接口
        matches = list(re.finditer(api_pattern, content))
        
        if not matches:
            print("未找到API接口定义")
            return False
        
        print(f"找到 {len(matches)} 个API接口")
        
        # 从后往前替换，避免位置偏移问题
        new_content = content
        counter = len(matches)
        
        for match in reversed(matches):
            old_number = match.group(1)
            interface_name = match.group(2)
            http_method = match.group(3)
            path = match.group(4)
            
            # 构建新的接口行
            old_line = match.group(0)
            new_line = f"- [ ] **{counter}** {interface_name} `{http_method} {path}`"
            
            # 替换内容
            new_content = new_content[:match.start()] + new_line + new_content[match.end():]
            
            print(f"接口 {counter}: {interface_name} (原序号: {old_number})")
            counter -= 1
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"\n✅ 成功重置 {len(matches)} 个API接口的序号")
        return True
        
    except FileNotFoundError:
        print(f"❌ 文件不存在: {file_path}")
        return False
    except Exception as e:
        print(f"❌ 处理文件时出错: {str(e)}")
        return False

def count_api_interfaces(file_path):
    """
    统计API接口数量
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        dict: 各控制器的接口数量统计
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计各控制器的接口数量
        controller_pattern = r'### \*\*(\w+Controller) \((\d+)\)\*\*'
        api_pattern = r'- \[ \] \*\*(\d+)\*\* (.+?) `(GET|POST|PUT|DELETE|PATCH) (.+?)`'
        
        controllers = re.findall(controller_pattern, content)
        apis = re.findall(api_pattern, content)
        
        stats = {
            'total_controllers': len(controllers),
            'total_apis': len(apis),
            'controllers': {}
        }
        
        # 按控制器分组统计
        current_controller = None
        controller_api_count = 0
        
        lines = content.split('\n')
        for line in lines:
            # 检查是否是控制器标题
            controller_match = re.match(r'### \*\*(\w+Controller)', line)
            if controller_match:
                if current_controller:
                    stats['controllers'][current_controller] = controller_api_count
                current_controller = controller_match.group(1)
                controller_api_count = 0
            
            # 检查是否是API接口
            elif re.match(api_pattern, line):
                controller_api_count += 1
        
        # 添加最后一个控制器
        if current_controller:
            stats['controllers'][current_controller] = controller_api_count
        
        return stats
        
    except Exception as e:
        print(f"❌ 统计接口数量时出错: {str(e)}")
        return None

def main():
    """主函数"""
    file_path = '.cursor/rules/dev-api-guidelines-pyapi.mdc'
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        sys.exit(1)
    
    print("🚀 开始重置API接口序号...")
    print(f"📁 文件路径: {file_path}")
    print("-" * 50)
    
    # 统计重置前的接口数量
    print("📊 重置前统计:")
    stats_before = count_api_interfaces(file_path)
    if stats_before:
        print(f"总控制器数: {stats_before['total_controllers']}")
        print(f"总接口数: {stats_before['total_apis']}")
        for controller, count in stats_before['controllers'].items():
            print(f"  {controller}: {count} 个接口")
    
    print("-" * 50)
    
    # 重置序号
    success = reset_api_numbers(file_path)
    
    if success:
        print("-" * 50)
        print("📊 重置后统计:")
        stats_after = count_api_interfaces(file_path)
        if stats_after:
            print(f"总控制器数: {stats_after['total_controllers']}")
            print(f"总接口数: {stats_after['total_apis']}")
            for controller, count in stats_after['controllers'].items():
                print(f"  {controller}: {count} 个接口")
        
        print("\n🎉 API接口序号重置完成！")
        print(f"📈 总计处理了 {stats_after['total_apis'] if stats_after else 0} 个API接口")
    else:
        print("\n❌ API接口序号重置失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
