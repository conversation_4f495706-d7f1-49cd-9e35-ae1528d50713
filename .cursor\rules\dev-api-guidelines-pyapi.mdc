---
description: API接口列表与对接规范文档 - API对接对接
globs: ["php/api/**/*.php", "php/api/**/*.json", "php/api/**/.env*", "php/api/**/routes/**", "php/api/**/config/**"]
alwaysApply: true
---

# API接口列表与对接规范文档

## 🚨 **AI平台选择功能升级说明** (2025-08-04)

### **升级概述**
本次升级为所有AI生成接口添加了用户平台选择功能，支持用户主动选择AI平台或使用智能推荐系统。

### **新增接口 (12个)**
- **平台选项接口**: 获取可用的AI平台选项列表
- **推荐平台接口**: 获取基于用户偏好的AI平台推荐

### **升级的接口模块**
- **ImageController**: 图像生成 + 平台选择
- **VideoController**: 视频生成 + 平台选择
- **AudioController**: 音频处理 + 平台选择
- **VoiceController**: 语音合成 + 平台选择
- **MusicController**: 音乐生成 + 平台选择
- **StoryController**: 故事生成 + 平台选择
- **SoundController**: 音效生成 + 平台选择

### **向后兼容性**
- 所有现有接口保持完全兼容
- `platform` 参数为可选，不指定时使用智能推荐
- 响应格式在现有基础上增加平台相关字段

## 🚨 **环境切换机制规范**

### **架构边界说明**
基于 `index-new.mdc` 架构规范，工具API接口服务负责环境切换机制的实现：
- **环境切换配置**：在 `php/api/config/ai.php` 中实现
- **服务客户端**：使用 `AiServiceClient` 和 `ThirdPartyServiceClient`
- **模拟服务**：`php/aiapi` 和 `php/thirdapi` 仅负责模拟，不包含环境切换逻辑





## 📋 API接口认证机制规范

### 支持的认证方式
1. **Bearer Token方式**（推荐）：
   ```
   Authorization: Bearer {token}
   ```

2. **URL参数方式**（兼容性）：
   ```
   ?token={token}
   ```

### 认证成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "2MBR9xSqRNoxR3INDWyCJbpklPDKZt2wJbWjIOP5ny07W",
    "user": {
      "id": 3
    }
  },
  "timestamp": 1754186310,
  "request_id": "req_688ec24661d5a_acf7c8e0"
}
```

### 认证失败响应
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```

### 📋 统一请求和响应格式标准

#### 请求数据示例模板(待补充完善)



#### ✅ 成功响应格式
```php
{
    "code": 200,                    // 业务状态码（成功）
    "message": "操作成功",           // 响应消息
    "data": {                       // 响应数据
        // 具体业务数据
    },
    "timestamp": 1640995200,        // 时间戳
    "request_id": "req_abc123_def456" // 请求ID
}
```

#### ❌ 正常业务错误响应格式
```php
{
    "code": 1006,                   // 业务错误码
    "message": "积分不足",           // 业务错误码描述
    "data": null,                   // 错误数据（可选）
    "timestamp": 1640995200,        // 时间戳
    "request_id": "req_abc123_def456" // 请求ID
}
```

#### ❌ 异业务错误响应格式
```php
{
    "code": 5002,                   // 5002=控制器异常，5003=服务层异常，详细异常信息请查看 Lumen 10 日志
    "message": "具体业务名称失败",   // 业务错误码描述
    "data": null,                   // 错误数据（可选）
    "timestamp": 1640995200,        // 时间戳
    "request_id": "req_abc123_def456" // 请求ID
}
```

## 📋 API接口列表

### **AuthController (6)**
- [ ] **1** 用户注册 `POST /py-api/auth/register`
  - 请求参数：`username, password, email` (email可选)
  - 成功响应：`200` - success
  - 错误响应：`422` - 参数验证失败, `1005` - 用户已存在, `5002` - 用户注册失败, `5003` - 用户注册失败

- [ ] **2** 用户登录 `POST /py-api/auth/login`
  - 请求参数：`username, password`
  - 成功响应：`200` - success
  - 错误响应：`422` - 参数验证失败, `1004` - 用户未注册, `5002` - 用户登录失败, `5003` - 用户登录失败

- [ ] **3** Token验证 `GET /py-api/auth/verify`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - Token验证成功
  - 错误响应：`401` - Token无效或过期, `5002` - Token验证失败, `5003` - Token验证失败

- [ ] **4** 用户登出 `POST /py-api/auth/logout`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 登出成功
  - 错误响应：`401` - 请登录后操作, `5002` - 用户登出失败, `5003` - 用户登出失败

- [ ] **5** 忘记密码 `POST /py-api/auth/forgot-password`
  - 请求参数：`email`
  - 成功响应：`200` - 密码重置邮件发送成功
  - 错误响应：`422` - 参数验证失败, `401` - 用户不存在, `5002` - 忘记密码失败, `5003` - 忘记密码失败

- [ ] **6** 重置密码 `POST /py-api/auth/reset-password`
  - 请求参数：`token, new_password, password_confirmation`
  - 成功响应：`200` - 密码重置成功
  - 错误响应：`422` - 参数验证失败, `401` - 重置令牌无效或已过期, `5002` - 密码重置失败, `5003` - 密码重置失败

### **UserController (4)**
- [ ] **7** 获取用户个人信息 `GET /py-api/user/profile`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取用户个人信息失败, `5003` - 获取用户个人信息失败

- [ ] **8** 更新用户资料 `PUT /py-api/user/profile`
  - 请求参数：`nickname, email, avatar` (可选)
  - 成功响应：`200` - 用户资料更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `1010` - 没有可更新的字段, `1009` - 邮箱已被使用, `5002` - 用户资料更新失败, `5003` - 用户资料更新失败

- [ ] **9** 更新用户偏好设置 `PUT /py-api/user/preferences`
  - 请求参数：`language, timezone, email_notifications, push_notifications, ai_preferences, ui_preferences, workflow_preferences, default_ai_model, auto_save_interval, show_tutorials` (可选)
  - 成功响应：`200` - 偏好设置更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 用户偏好设置更新失败, `5003` - 用户偏好设置更新失败

- [ ] **10** 获取用户偏好设置 `GET /py-api/user/preferences`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取用户偏好设置失败, `5003` - 获取用户偏好设置失败

### **PointsController (3)**
- [ ] **11** 积分余额查询 `GET /py-api/points/balance`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 积分余额查询失败, `5003` - 积分余额查询失败

- [ ] **12** 积分充值 `POST /py-api/points/recharge`
  - 请求参数：`amount, payment_method`
  - 成功响应：`200` - 充值成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 积分充值失败, `5003` - 积分充值失败

- [ ] **13** 积分交易记录 `GET /py-api/points/transactions`
  - 请求参数：`page, per_page, status, business_type` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 积分交易记录查询失败, `5003` - 积分交易记录查询失败

### **CreditsController (3)**
- [ ] **14** 积分预检查 `POST /py-api/credits/check`
  - 请求参数：`amount, business_type, business_id` (可选)
  - 成功响应：`200` - 积分检查完成
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `400` - 无效的业务类型, `5002` - 积分预检查失败, `5003` - 积分预检查失败

- [ ] **15** 积分冻结 `POST /py-api/credits/freeze`
  - 请求参数：`amount, business_type, business_id, timeout_seconds` (可选)
  - 成功响应：`200` - 积分冻结成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `400` - 无效的业务类型, `1006` - 积分不足, `5002` - 积分冻结失败, `5003` - 积分冻结失败

- [ ] **16** 积分返还 `POST /py-api/credits/refund`
  - 请求参数：`freeze_id, return_reason` (可选)
  - 成功响应：`200` - 积分返还成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 找不到对应的业务记录, `409` - 积分已经被扣除, `5002` - 积分返还失败, `5003` - 积分返还失败

### **UserGrowthController (10)**
- [ ] **17** 获取用户成长信息 `GET /py-api/user-growth/profile`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 用户不存在, `5002` - 获取用户成长信息失败, `5003` - 获取用户成长信息失败

- [ ] **18** 获取排行榜 `GET /py-api/user-growth/leaderboard`
  - 请求参数：`type, period, limit` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取排行榜失败, `5003` - 获取排行榜失败

- [ ] **19** 获取每日任务 `GET /py-api/user-growth/daily-tasks`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取每日任务失败, `5003` - 获取每日任务失败

- [ ] **20** 获取成长历史 `GET /py-api/user-growth/history`
  - 请求参数：`type, date_from, date_to, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取成长历史失败, `5003` - 获取成长历史失败

- [ ] **21** 获取统计信息 `GET /py-api/user-growth/statistics`
  - 请求参数：`period` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取成长统计失败, `5003` - 获取成长统计失败

- [ ] **22** 设置成长目标 `POST /py-api/user-growth/goals`
  - 请求参数：`goals` (数组，包含type和target)
  - 成功响应：`200` - 成长目标设置成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 设置成长目标失败, `5003` - 设置成长目标失败

- [ ] **23** 获取推荐 `GET /py-api/user-growth/recommendations`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取成长推荐失败, `5003` - 获取成长推荐失败

- [ ] **24** 获取里程碑 `GET /py-api/user-growth/milestones`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取里程碑失败, `5003` - 获取里程碑失败

- [ ] **25** 完成每日任务 `POST /py-api/user-growth/daily-tasks/{id}/complete`
  - 请求参数：`task_id, completion_data` (可选)
  - 成功响应：`200` - 每日任务完成
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 任务不存在, `1008` - 任务已完成, `5002` - 完成每日任务失败, `5003` - 完成每日任务失败

- [ ] **26** 完成成就 `POST /py-api/user-growth/achievements/{id}/complete`
  - 请求参数：`achievement_id, progress_data` (可选)
  - 成功响应：`200` - 成就完成
  - 错误响应：`401` - 认证失败, `404` - 成就不存在, `422` - 参数错误, `1008` - 成就已完成, `5002` - 完成成就失败, `5003` - 完成成就失败

### **NotificationController (6)**
- [ ] **27** 获取用户通知列表 `GET /py-api/notifications`
  - 请求参数：`type, status, page, per_page` (可选)
  - 成功响应：`200` - 用户通知获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取用户通知列表失败, `5003` - 获取用户通知列表失败

- [ ] **28** 标记通知为已读 `PUT /py-api/notifications/mark-read`
  - 请求参数：`notification_ids` (数组)
  - 成功响应：`200` - 通知已标记为已读
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 标记通知已读失败, `5003` - 标记通知已读失败

- [ ] **29** 标记所有通知为已读 `PUT /py-api/notifications/mark-all-read`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 所有通知已标记为已读
  - 错误响应：`401` - 认证失败, `5002` - 标记所有通知已读失败, `5003` - 标记所有通知已读失败

- [ ] **30** 获取通知统计 `GET /py-api/notifications/stats`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 通知统计获取成功
  - 错误响应：`401` - 认证失败, `5002` - 获取通知统计失败, `5003` - 获取通知统计失败

- [ ] **31** 删除通知 `DELETE /py-api/notifications/{id}`
  - 请求参数：通知ID (路径参数)
  - 成功响应：`200` - 通知删除成功
  - 错误响应：`401` - 认证失败, `404` - 通知不存在, `5002` - 删除通知失败, `5003` - 删除通知失败

- [ ] **32** 发送通知 `POST /py-api/notifications/send`
  - 请求参数：`recipients, title, content, type` (可选)
  - 成功响应：`200` - 通知发送成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可发送系统通知, `1092` - 发送频率超限，请稍后再试, `5002` - 发送通知失败, `5003` - 发送通知失败

### **ConfigController (7)**
- [ ] **33** 获取配置列表 `GET /py-api/config`
  - 请求参数：`category, key, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可查看系统配置, `5002` - 获取配置列表失败, `5003` - 获取配置列表失败

- [ ] **34** 获取公开配置 `GET /py-api/config/public`
  - 请求参数：无
  - 成功响应：`200` - success
  - 错误响应：`5002` - 获取公开配置失败, `5003` - 获取公开配置失败

- [ ] **35** 更新配置 `PUT /py-api/config/{id}`
  - 请求参数：`value, description` (可选)
  - 成功响应：`200` - 配置更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可修改系统配置, `404` - 配置不存在, `5002` - 配置更新失败, `5003` - 配置更新失败

- [ ] **36** 批量更新配置 `PUT /py-api/config/batch`
  - 请求参数：`configs` (数组)
  - 成功响应：`200` - 配置批量更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可修改系统配置, `5002` - 批量配置更新失败, `5003` - 批量配置更新失败

- [ ] **37** 重置配置 `POST /py-api/config/{id}/reset`
  - 请求参数：配置ID (路径参数)
  - 成功响应：`200` - 配置重置成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可重置系统配置, `404` - 配置不存在, `5002` - 配置重置失败, `5003` - 配置重置失败

- [ ] **38** 获取配置历史 `GET /py-api/config/{id}/history`
  - 请求参数：配置ID (路径参数), `page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可查看配置历史, `404` - 配置不存在, `5002` - 获取配置历史失败, `5003` - 获取配置历史失败

- [ ] **39** 验证配置 `POST /py-api/config/validate`
  - 请求参数：`key, value, type` (可选)
  - 成功响应：`200` - 配置值验证通过
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可验证配置, `5002` - 配置值验证失败, `5003` - 配置值验证失败

### **AiTaskController (8)**
- [ ] **40** 获取任务列表 `GET /py-api/tasks`
  - 请求参数：`status, type, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取任务列表失败, `5003` - 获取任务列表失败

- [ ] **41** 获取任务详情 `GET /py-api/tasks/{id}`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `403` - 用户无权查看该任务, `5002` - 获取任务详情失败, `5003` - 获取任务详情失败

- [ ] **42** 获取任务统计 `GET /py-api/tasks/stats`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取任务统计失败, `5003` - 获取任务统计失败

- [ ] **43** 取消任务 `POST /py-api/tasks/{id}/cancel`
  - 请求参数：任务ID (路径参数), `reason` (可选)
  - 成功响应：`200` - 任务已成功取消
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `403` - 用户无权取消该任务, `1007` - 任务状态不允许取消, `5002` - 任务取消失败, `5003` - 任务取消失败

- [ ] **44** 重试任务 `POST /py-api/tasks/{id}/retry`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - 任务重试成功
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `403` - 无权限操作此任务, `1007` - 任务重试次数已达上限, `5002` - 任务重试失败, `5003` - 任务重试失败

- [ ] **45** 批量查询任务状态 `GET /py-api/tasks/batch/status`
  - 请求参数：`task_ids` (数组)
  - 成功响应：`200` - 批量查询成功
  - 错误响应：`401` - 认证失败, `1010` - 任务ID列表不能为空, `1010` - 批量查询任务数量不能超过100个, `5002` - 批量查询失败, `5003` - 批量查询失败

- [ ] **46** 查询任务恢复状态 `GET /py-api/tasks/{id}/recovery`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - 查询成功
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `403` - 用户无权查看该任务, `5002` - 查询失败, `5003` - 查询失败

- [ ] **47** 获取超时配置 `GET /py-api/tasks/timeout-config`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 获取超时配置成功
  - 错误响应：`401` - 认证失败, `5002` - 获取超时配置失败, `5003` - 获取超时配置失败

### **RecommendationController (8)**
- [ ] **48** 获取内容推荐 `GET /py-api/recommendations/content`
  - 请求参数：`type, category, limit` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取推荐失败, `5003` - 获取推荐失败

- [ ] **49** 获取用户推荐 `GET /py-api/recommendations/users`
  - 请求参数：`type, limit` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取用户推荐失败, `5003` - 获取用户推荐失败

- [ ] **50** 获取话题推荐 `GET /py-api/recommendations/topics`
  - 请求参数：`type, category, limit` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取话题推荐失败, `5003` - 获取话题推荐失败

- [ ] **51** 提交推荐反馈 `POST /py-api/recommendations/feedback`
  - 请求参数：`recommendation_id, item_id, item_type, action, rating` (可选)
  - 成功响应：`200` - 反馈提交成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 提交反馈失败, `5003` - 提交反馈失败

- [ ] **52** 获取推荐偏好 `GET /py-api/recommendations/preferences`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取偏好设置失败, `5003` - 获取偏好设置失败

- [ ] **53** 更新推荐偏好 `PUT /py-api/recommendations/preferences`
  - 请求参数：`preferences` (对象)
  - 成功响应：`200` - 推荐设置更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 更新偏好设置失败, `5003` - 更新偏好设置失败

- [ ] **54** 获取推荐分析 `GET /py-api/recommendations/analytics`
  - 请求参数：`period` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取推荐统计失败, `5003` - 获取推荐统计失败

- [ ] **55** 获取个性化推荐 `GET /py-api/recommendations/personalized`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取推荐失败, `5003` - 获取推荐失败

### **ProjectController (8)**
- [ ] **56** 基于故事创建项目 `POST /py-api/projects/create-with-story`
  - 请求参数：`style_id, story_content, title` (可选)
  - 成功响应：`200` - 项目创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 风格不存在, `400` - 创建项目过于频繁，请稍后再试, `5002` - 项目创建失败, `5003` - 项目创建失败

- [ ] **57** 确认项目标题 `PUT /py-api/projects/{id}/confirm-title`
  - 请求参数：项目ID (路径参数), `use_ai_title, custom_title` (可选)
  - 成功响应：`200` - 标题确认成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 项目不存在, `1007` - 标题已经确认，无法修改, `1010` - 自定义标题不能为空, `5002` - 标题确认失败, `5003` - 标题确认失败

- [ ] **58** 获取我的项目列表 `GET /py-api/projects/my-projects`
  - 请求参数：`status, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取项目列表失败, `5003` - 获取项目列表失败

- [ ] **59** 获取项目详情 `GET /py-api/projects/{id}`
  - 请求参数：项目ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 项目不存在, `5002` - 获取项目详情失败, `5003` - 获取项目详情失败

- [ ] **60** 获取项目列表 `GET /py-api/projects/list`
  - 请求参数：`status, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取项目列表失败, `5003` - 获取项目列表失败

- [ ] **61** 创建项目 `POST /py-api/projects/create`
  - 请求参数：`title, description, type` (可选)
  - 成功响应：`200` - 项目创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `400` - 创建项目过于频繁，请稍后再试, `5002` - 项目创建失败, `5003` - 项目创建失败

- [ ] **62** 更新项目 `PUT /py-api/projects/{id}`
  - 请求参数：项目ID (路径参数), `title, description, status` (可选)
  - 成功响应：`200` - 项目更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 项目不存在, `1010` - 无效的项目状态, `5002` - 项目更新失败, `5003` - 项目更新失败

- [ ] **63** 删除项目 `DELETE /py-api/projects/{id}`
  - 请求参数：项目ID (路径参数)
  - 成功响应：`200` - 项目删除成功
  - 错误响应：`401` - 认证失败, `404` - 项目不存在, `5002` - 项目删除失败, `5003` - 项目删除失败

### **FileController (5)**
- [ ] **64** 上传文件 `POST /py-api/files/upload`
  - 请求参数：`file, folder, is_public, is_temporary` (folder、is_public、is_temporary可选)
  - 成功响应：`200` - 文件上传成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `1010` - 文件上传失败, `1010` - 不支持的文件类型, `5002` - 文件上传失败, `5003` - 文件上传失败

- [ ] **65** 获取文件列表 `GET /py-api/files/list`
  - 请求参数：`folder, type, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取文件列表失败, `5003` - 获取文件列表失败

- [ ] **66** 获取文件详情 `GET /py-api/files/{id}`
  - 请求参数：文件ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 文件不存在, `5002` - 获取文件详情失败, `5003` - 获取文件详情失败

- [ ] **67** 删除文件 `DELETE /py-api/files/{id}`
  - 请求参数：文件ID (路径参数)
  - 成功响应：`200` - 文件删除成功
  - 错误响应：`401` - 认证失败, `404` - 文件不存在, `5002` - 文件删除失败, `5003` - 文件删除失败

- [ ] **68** 下载文件 `GET /py-api/files/{id}/download`
  - 请求参数：文件ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 文件不存在, `1007` - 文件已过期, `5002` - 获取下载URL失败, `5003` - 获取下载URL失败

### **ImageController (4)** 🚨 升级
- [ ] **69** 生成图像 `POST /py-api/images/generate`
  - 请求参数：`prompt, character_id, project_id, generation_params, platform` (platform可选，不指定时使用智能推荐)
  - 成功响应：`200` - 图像生成任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 角色不存在, `404` - 没有可用的图像生成模型, `1012` - 图像生成服务当前不可用, `5002` - 图像生成失败, `5003` - 图像生成失败

- [ ] **70** 获取图像生成状态 `GET /py-api/images/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `5002` - 获取图像生成状态失败, `5003` - 获取图像生成状态失败

- [ ] **71** 获取图像生成结果 `GET /py-api/images/{id}/result`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `400` - 任务尚未完成, `5002` - 获取图像生成结果失败, `5003` - 获取图像生成结果失败

- [ ] **72** 批量生成图像 `POST /py-api/images/batch-generate`
  - 请求参数：`prompts, project_id, common_params` (可选)
  - 成功响应：`200` - 批量图像生成任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 批量图像生成失败, `5003` - 批量图像生成失败

### **VideoController (3)** 🚨 升级
- [ ] **73** 生成视频 `POST /py-api/videos/generate`
  - 请求参数：`prompt, duration, aspect_ratio, quality, fps, style, project_id, platform` (platform可选，不指定时使用智能推荐)
  - 成功响应：`200` - 视频生成任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 没有可用的视频生成模型, `1012` - 视频生成服务当前不可用, `5002` - 视频生成失败, `5003` - 视频生成失败

- [ ] **74** 获取视频生成状态 `GET /py-api/videos/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `5002` - 获取视频状态失败, `5003` - 获取视频状态失败

- [ ] **75** 获取视频生成结果 `GET /py-api/videos/{id}/result`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `400` - 任务尚未完成, `5002` - 获取视频结果失败, `5003` - 获取视频结果失败

### **AudioController (4)** 🚨 升级
- [ ] **76** 音频混合 `POST /py-api/audio/mix`
  - 请求参数：`audio_urls, mix_config, project_id, platform` (mix_config、project_id、platform可选，platform不指定时使用智能推荐)
  - 成功响应：`200` - 音频混音任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `1006` - 积分不足, `5002` - 音频混音失败, `5003` - 音频混音失败

- [ ] **77** 获取音频混合状态 `GET /py-api/audio/mix/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `5002` - 获取音频混合状态失败, `5003` - 获取音频混合状态失败

- [ ] **78** 音频增强 `POST /py-api/audio/enhance`
  - 请求参数：`audio_url, enhance_config, project_id, platform` (enhance_config、project_id、platform可选，platform不指定时使用智能推荐)
  - 成功响应：`200` - 音频增强任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `1006` - 积分不足, `5002` - 音频增强失败, `5003` - 音频增强失败

- [ ] **79** 获取音频增强状态 `GET /py-api/audio/enhance/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `5002` - 获取音频增强状态失败, `5003` - 获取音频增强状态失败

### **MusicController (4)** 🚨 升级
- [ ] **80** 生成音乐 `POST /py-api/music/generate`
  - 请求参数：`prompt, genre, mood, duration, project_id, platform` (platform可选，不指定时使用智能推荐)
  - 成功响应：`200` - 音乐生成任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 没有可用的音乐生成模型, `1012` - 音乐生成服务当前不可用, `1006` - 积分不足, `5002` - 音乐生成失败, `5003` - 音乐生成失败

- [ ] **81** 获取音乐生成状态 `GET /py-api/music/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `5002` - 获取音乐生成状态失败, `5003` - 获取音乐生成状态失败

- [ ] **82** 获取音乐生成结果 `GET /py-api/music/{id}/result`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `400` - 任务尚未完成, `5002` - 获取音乐生成结果失败, `5003` - 获取音乐生成结果失败

- [ ] **83** 批量生成音乐 `POST /py-api/music/batch-generate`
  - 请求参数：`prompts, project_id, genre, mood` (可选)
  - 成功响应：`200` - 批量音乐生成任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `1006` - 积分不足, `5002` - 批量音乐生成失败, `5003` - 批量音乐生成失败

### **StoryController (2)** 🚨 升级
- [ ] **84** 生成故事 `POST /py-api/stories/generate`
  - 请求参数：`prompt, style_id, project_id, length, genre, platform` (platform可选，不指定时使用智能推荐)
  - 成功响应：`200` - 故事生成任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 风格不存在, `404` - 没有可用的故事生成模型, `1012` - 故事生成服务当前不可用, `1006` - 积分不足, `5002` - 故事生成失败, `5003` - 故事生成失败

- [ ] **85** 获取故事生成状态 `GET /py-api/stories/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `5002` - 获取故事生成状态失败, `5003` - 获取故事生成状态失败

### **CharacterController (9)**
- [ ] **86** 获取角色分类 `GET /py-api/characters/categories`
  - 请求参数：`parent_id, include_children` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取分类失败, `5003` - 获取分类失败

- [ ] **87** 获取角色库列表 `GET /py-api/characters/list`
  - 请求参数：`category_id, page, per_page, search` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取角色列表失败, `5003` - 获取角色列表失败

- [ ] **88** 获取角色详情 `GET /py-api/characters/{id}`
  - 请求参数：角色ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 角色不存在, `5002` - 获取角色详情失败, `5003` - 获取角色详情失败

- [ ] **89** 生成角色 `POST /py-api/characters/generate`
  - 请求参数：`prompt, style, project_id` (可选)
  - 成功响应：`200` - 角色生成任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 没有可用的角色生成模型, `1012` - 角色生成服务当前不可用, `1006` - 积分不足, `5002` - 角色生成失败, `5003` - 角色生成失败

- [ ] **90** 获取角色推荐 `GET /py-api/characters/recommendations`
  - 请求参数：`type, limit` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取推荐失败, `5003` - 获取推荐失败

- [ ] **91** 绑定角色 `POST /py-api/characters/bind`
  - 请求参数：`character_id, reason` (可选)
  - 成功响应：`200` - 角色绑定成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 角色不存在, `400` - 角色已经绑定, `5002` - 角色绑定失败, `5003` - 角色绑定失败

- [ ] **92** 获取我的角色绑定 `GET /py-api/characters/bindings`
  - 请求参数：`page, per_page, status` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取绑定列表失败, `5003` - 获取绑定列表失败

- [ ] **93** 更新角色绑定 `PUT /py-api/characters/bindings/{id}`
  - 请求参数：绑定ID (路径参数), `binding_name, custom_description, custom_config, is_favorite` (可选)
  - 成功响应：`200` - 绑定更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 绑定不存在, `5002` - 绑定更新失败, `5003` - 绑定更新失败

- [ ] **94** 解绑角色 `DELETE /py-api/characters/unbind`
  - 请求参数：`character_id`
  - 成功响应：`200` - 角色解绑成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 绑定不存在, `1007` - 绑定已解除, `5002` - 角色解绑失败, `5003` - 角色解绑失败

### **StyleController (4)**
- [ ] **95** 获取风格列表 `GET /py-api/styles/list`
  - 请求参数：`page, per_page, category, search` (可选)
  - 成功响应：`200` - success
  - 错误响应：`422` - 参数验证失败, `5002` - 搜索失败, `5003` - 搜索失败

- [ ] **96** 获取风格详情 `GET /py-api/styles/{id}`
  - 请求参数：风格ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`404` - 风格不存在, `5002` - 获取风格详情失败, `5003` - 获取风格详情失败

- [ ] **97** 获取热门风格 `GET /py-api/styles/popular`
  - 请求参数：`limit` (可选)
  - 成功响应：`200` - success
  - 错误响应：`422` - 参数验证失败, `5002` - 获取推荐风格失败, `5003` - 获取推荐风格失败

- [ ] **98** 创建风格 `POST /py-api/styles/create`
  - 请求参数：`name, description, category, tags` (可选)
  - 成功响应：`200` - 风格创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 风格创建失败, `5003` - 风格创建失败

### **TemplateController (7)**
- [ ] **99** 创建模板 `POST /py-api/templates/create`
  - 请求参数：`name, description, type, category, source_type, source_id, tags, visibility, configuration` (可选)
  - 成功响应：`200` - 模板创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 来源资源或项目不存在, `1098` - 模板创建失败, `5002` - 模板创建失败, `5003` - 模板创建失败

- [ ] **100** 使用模板 `POST /py-api/templates/{id}/use`
  - 请求参数：模板ID (路径参数), `name, customization, apply_settings` (可选)
  - 成功响应：`200` - 模板应用成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 模板不存在或无权限使用, `1098` - 基于模板创建失败, `5002` - 模板使用失败, `5003` - 模板使用失败

- [ ] **101** 获取模板市场 `GET /py-api/templates/marketplace`
  - 请求参数：`category, type, sort, page, per_page, search` (可选)
  - 成功响应：`200` - success
  - 错误响应：`422` - 参数验证失败, `5002` - 获取模板市场失败, `5003` - 获取模板市场失败

- [ ] **102** 获取我的模板 `GET /py-api/templates/my-templates`
  - 请求参数：`type, category, visibility, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取用户模板失败, `5003` - 获取用户模板失败

- [ ] **103** 获取模板详情 `GET /py-api/templates/{id}/detail`
  - 请求参数：模板ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`404` - 模板不存在或无权限访问, `5002` - 获取模板详情失败, `5003` - 获取模板详情失败

- [ ] **104** 更新模板 `PUT /py-api/templates/{id}`
  - 请求参数：模板ID (路径参数), `name, description, tags, visibility, configuration` (可选)
  - 成功响应：`200` - 模板更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 模板不存在或无权限修改, `5002` - 模板更新失败, `5003` - 模板更新失败

- [ ] **105** 删除模板 `DELETE /py-api/templates/{id}`
  - 请求参数：模板ID (路径参数)
  - 成功响应：`200` - 模板删除成功
  - 错误响应：`401` - 认证失败, `404` - 模板不存在或无权限删除, `5002` - 模板删除失败, `5003` - 模板删除失败

### **VoiceController (7)** 🚨 升级
- [ ] **106** 语音合成 `POST /py-api/voice/synthesize`
  - 请求参数：`text, voice_id, character_id, project_id, speed, pitch, volume, platform` (platform可选，不指定时使用智能推荐)
  - 成功响应：`200` - 语音合成任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 角色不存在, `404` - 没有可用的语音合成模型, `1012` - 语音合成服务当前不可用, `1006` - 积分不足, `5002` - 语音合成失败, `5003` - 语音合成失败

- [ ] **107** 获取语音合成状态 `GET /py-api/voice/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `5002` - 获取语音合成状态失败, `5003` - 获取语音合成状态失败

- [ ] **108** 批量语音合成 `POST /py-api/voice/batch-synthesize`
  - 请求参数：`texts, project_id, voice_id, character_id, common_params` (可选)
  - 成功响应：`200` - 批量语音合成任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `1006` - 积分不足, `5002` - 批量语音合成失败, `5003` - 批量语音合成失败

- [ ] **109** 语音克隆 `POST /py-api/voice/clone`
  - 请求参数：`audio_samples, voice_name, character_id, project_id` (可选)
  - 成功响应：`200` - 音色克隆任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 角色不存在, `1099` - 没有可用的音色克隆模型, `1006` - 积分不足, `5002` - 音色克隆失败, `5003` - 音色克隆失败

- [ ] **110** 获取语音克隆状态 `GET /py-api/voice/clone/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `5002` - 获取语音克隆状态失败, `5003` - 获取语音克隆状态失败

- [ ] **111** 自定义语音 `POST /py-api/voice/custom`
  - 请求参数：`voice_description, reference_audio, character_id, project_id` (可选)
  - 成功响应：`200` - 自定义音色生成任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 没有可用的音色生成模型, `1006` - 积分不足, `5002` - 自定义音色生成失败, `5003` - 自定义音色生成失败

- [ ] **112** 获取自定义语音状态 `GET /py-api/voice/custom/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `5002` - 获取自定义语音状态失败, `5003` - 获取自定义语音状态失败

### **AiGenerationController (4)**
- [ ] **113** 文本生成 `POST /py-api/ai/text/generate`
  - 请求参数：`prompt, model, max_tokens, temperature, project_id` (可选)
  - 成功响应：`200` - 文本生成任务已创建
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 没有可用的文本生成模型, `1012` - 模型服务当前不可用, `1006` - 积分不足, `5002` - 文本生成任务创建失败, `5003` - 文本生成任务创建失败

- [ ] **114** 获取任务状态 `GET /py-api/ai/tasks/{id}`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `5002` - 获取任务状态失败, `5003` - 获取任务状态失败

- [ ] **115** 获取用户任务 `GET /py-api/ai/tasks`
  - 请求参数：`page, per_page, status` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取任务列表失败, `5003` - 获取任务列表失败

- [ ] **116** 重试任务 `POST /py-api/ai/tasks/{id}/retry`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - 任务重试成功
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1007` - 任务无法重试, `5002` - 任务重试失败, `5003` - 任务重试失败

### **AiModelController (26)** 🚨 升级
- [ ] **117** 获取可用模型 `GET /py-api/ai-models/available`
  - 请求参数：`type, category, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取模型列表失败, `5003` - 获取模型列表失败

- [ ] **118** 获取模型详情 `GET /py-api/ai-models/{model_id}/detail`
  - 请求参数：模型ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 模型不存在, `5002` - 获取模型详情失败, `5003` - 获取模型详情失败

- [ ] **119** 测试模型 `POST /py-api/ai-models/{model_id}/test`
  - 请求参数：模型ID (路径参数), `test_data` (可选)
  - 成功响应：`200` - 模型测试完成
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 模型测试失败, `5003` - 模型测试失败

- [ ] **120** 使用统计 `GET /py-api/ai-models/usage-stats`
  - 请求参数：`period, model_id` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取使用统计失败, `5003` - 获取使用统计失败

- [ ] **121** 收藏模型 `POST /py-api/ai-models/{model_id}/favorite`
  - 请求参数：模型ID (路径参数), `action` (favorite/unfavorite)
  - 成功响应：`200` - 收藏操作成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 收藏操作失败, `5003` - 收藏操作失败

- [ ] **122** 获取收藏列表 `GET /py-api/ai-models/favorites`
  - 请求参数：`page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取收藏列表失败, `5003` - 获取收藏列表失败

- [ ] **123** 获取模型列表 `GET /py-api/ai-models/list`
  - 请求参数：`category, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`422` - 参数验证失败, `5002` - 获取AI模型列表失败, `5003` - 获取AI模型列表失败

- [ ] **124** 切换模型 `POST /py-api/ai-models/switch`
  - 请求参数：`model_id, model_type`
  - 成功响应：`200` - 模型切换成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 指定的AI模型不存在或不可用, `400` - 该模型需要高级会员权限, `5002` - 切换模型失败, `5003` - 切换模型失败

- [ ] **125** 选择最优平台 `POST /py-api/ai-models/select-platform`
  - 请求参数：`platform_id, model_type`
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 平台选择失败, `5003` - 平台选择失败

- [ ] **126** 检查平台健康 `GET /py-api/ai-models/platform-health/{platform}`
  - 请求参数：无
  - 成功响应：`200` - success
  - 错误响应：`5002` - 健康检查失败, `5003` - 健康检查失败

- [ ] **127** 获取所有平台健康状态 `GET /py-api/ai-models/platforms-health`
  - 请求参数：无
  - 成功响应：`200` - success
  - 错误响应：`5002` - 获取健康状态失败, `5003` - 获取健康状态失败

- [ ] **128** 获取平台统计 `GET /py-api/ai-models/platform-stats/{platform}`
  - 请求参数：无
  - 成功响应：`200` - success
  - 错误响应：`5002` - 获取统计数据失败, `5003` - 获取统计数据失败

- [ ] **129** 平台性能对比 `GET /py-api/ai-models/platform-comparison`
  - 请求参数：`platforms, metrics` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 请登录后操作, `422` - 参数验证失败, `5002` - 获取平台对比数据失败, `5003` - 获取平台对比数据失败

- [ ] **130** 按业务类型获取平台 `GET /py-api/ai-models/business-platforms`
  - 请求参数：`business_type, requirements` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 请登录后操作, `422` - 参数验证失败, `5002` - 获取平台列表失败, `5003` - 获取平台列表失败

🚨 **新增平台选择接口 (12个)**
- [ ] **131** 获取图像生成平台选项 `GET /py-api/ai-models/platform-options/image_generation`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取平台选项失败, `5003` - 获取平台选项失败

- [ ] **132** 获取图像生成推荐平台 `GET /py-api/ai-models/user-recommendations/image_generation`
  - 请求参数：`limit` (可选，默认3个)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取推荐平台失败, `5003` - 获取推荐平台失败

- [ ] **133** 获取视频生成平台选项 `GET /py-api/ai-models/platform-options/video_generation`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取平台选项失败, `5003` - 获取平台选项失败

- [ ] **134** 获取视频生成推荐平台 `GET /py-api/ai-models/user-recommendations/video_generation`
  - 请求参数：`limit` (可选，默认3个)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取推荐平台失败, `5003` - 获取推荐平台失败

- [ ] **135** 获取语音合成平台选项 `GET /py-api/ai-models/platform-options/voice_synthesis`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取平台选项失败, `5003` - 获取平台选项失败

- [ ] **136** 获取语音合成推荐平台 `GET /py-api/ai-models/user-recommendations/voice_synthesis`
  - 请求参数：`limit` (可选，默认3个)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取推荐平台失败, `5003` - 获取推荐平台失败

- [ ] **137** 获取音频处理平台选项 `GET /py-api/ai-models/platform-options/sound_generation`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取平台选项失败, `5003` - 获取平台选项失败

- [ ] **138** 获取音频处理推荐平台 `GET /py-api/ai-models/user-recommendations/sound_generation`
  - 请求参数：`limit` (可选，默认3个)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取推荐平台失败, `5003` - 获取推荐平台失败

- [ ] **139** 获取音乐生成平台选项 `GET /py-api/ai-models/platform-options/music_generation`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取平台选项失败, `5003` - 获取平台选项失败

- [ ] **140** 获取音乐生成推荐平台 `GET /py-api/ai-models/user-recommendations/music_generation`
  - 请求参数：`limit` (可选，默认3个)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取推荐平台失败, `5003` - 获取推荐平台失败

- [ ] **141** 获取文本生成平台选项 `GET /py-api/ai-models/platform-options/text_generation`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取平台选项失败, `5003` - 获取平台选项失败

- [ ] **142** 获取文本生成推荐平台 `GET /py-api/ai-models/user-recommendations/text_generation`
  - 请求参数：`limit` (可选，默认3个)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取推荐平台失败, `5003` - 获取推荐平台失败

### **SoundController (4)** 🚨 升级
- [ ] **143** 生成音效 `POST /py-api/sounds/generate`
  - 请求参数：`prompt, duration, style, project_id, platform` (platform可选，不指定时使用智能推荐)
  - 成功响应：`200` - 音效生成任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 没有可用的音效生成模型, `1012` - 音效生成服务当前不可用, `1006` - 积分不足, `5002` - 音效生成失败, `5003` - 音效生成失败

- [ ] **144** 获取生成状态 `GET /py-api/sounds/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `5002` - 获取音效生成状态失败, `5003` - 获取音效生成状态失败

- [ ] **145** 获取生成结果 `GET /py-api/sounds/{id}/result`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `400` - 任务尚未完成, `5002` - 获取音效生成结果失败, `5003` - 获取音效生成结果失败

- [ ] **146** 批量生成音效 `POST /py-api/sounds/batch-generate`
  - 请求参数：`prompts, project_id, common_params` (可选)
  - 成功响应：`200` - 批量音效生成任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `1006` - 积分不足, `5002` - 批量音效生成失败, `5003` - 批量音效生成失败

### **ProjectManagementController (6)**
- [ ] **147** 创建项目任务 `POST /py-api/project-management/tasks`
  - 请求参数：`project_id, task_name, description, priority, assigned_to, due_date` (可选)
  - 成功响应：`200` - 项目管理任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 项目不存在或无权限, `5002` - 项目管理任务创建失败, `5003` - 项目管理任务创建失败

- [ ] **148** 项目协作管理 `POST /py-api/project-management/collaborate`
  - 请求参数：`project_id, user_id, action, role, permissions` (可选)
  - 成功响应：`200` - 协作管理操作成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 项目不存在或无权限管理, `400` - 无效的操作类型, `1007` - 用户已经是项目协作者, `5002` - 协作管理失败, `5003` - 协作管理失败

- [ ] **149** 获取项目进度 `GET /py-api/project-management/progress`
  - 请求参数：`project_id, period` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 项目不存在或无权限访问, `5002` - 获取项目进度失败, `5003` - 获取项目进度失败

- [ ] **150** 分配项目资源 `POST /py-api/project-management/assign-resources`
  - 请求参数：`project_id, resource_type, resource_id, allocation, resource_data` (可选)
  - 成功响应：`200` - 资源分配成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 项目不存在或无权限, `5002` - 资源分配失败, `5003` - 资源分配失败

- [ ] **151** 获取项目统计 `GET /py-api/project-management/statistics`
  - 请求参数：`project_id, metrics, period` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 项目不存在或无权限访问, `5002` - 获取项目统计失败, `5003` - 获取项目统计失败

- [ ] **152** 获取项目里程碑 `GET /py-api/project-management/milestones`
  - 请求参数：`project_id, status` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 项目不存在或无权限访问, `5002` - 获取项目里程碑失败, `5003` - 获取项目里程碑失败

### **ResourceController (8)**
- [ ] **153** 生成资源 `POST /py-api/resources/generate`
  - 请求参数：`project_id, resource_type, generation_config, output_format, quality_level, batch_size` (可选)
  - 成功响应：`200` - 资源生成任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 项目不存在或无权限访问, `1006` - 积分不足, `400` - 不支持的资源类型, `5002` - 资源生成任务创建失败, `5003` - 资源生成任务创建失败

- [ ] **154** 获取资源状态 `GET /py-api/resources/{id}/status`
  - 请求参数：资源ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 资源不存在, `5002` - 获取资源状态失败, `5003` - 获取资源状态失败

- [ ] **155** 获取资源列表 `GET /py-api/resources/list`
  - 请求参数：`type, status, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取资源列表失败, `5003` - 获取资源列表失败

- [ ] **156** 删除资源 `DELETE /py-api/resources/{id}`
  - 请求参数：资源ID (路径参数)
  - 成功响应：`200` - 资源删除成功
  - 错误响应：`401` - 认证失败, `404` - 资源不存在, `5002` - 资源删除失败, `5003` - 资源删除失败

- [ ] **157** 获取下载信息 `GET /py-api/resources/{id}/download-info`
  - 请求参数：资源ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 资源不存在或无权限访问, `5002` - 获取下载信息失败, `5003` - 获取下载信息失败

- [ ] **158** 确认下载 `POST /py-api/resources/{id}/confirm-download`
  - 请求参数：资源ID (路径参数), `download_type` (可选)
  - 成功响应：`200` - 下载确认成功
  - 错误响应：`401` - 认证失败, `404` - 资源不存在或无权限访问, `422` - 参数验证失败, `5002` - 下载确认失败, `5003` - 下载确认失败

- [ ] **159** 获取我的资源 `GET /py-api/resources/my-resources`
  - 请求参数：`type, status, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取我的资源失败, `5003` - 获取我的资源失败

- [ ] **160** 更新资源状态 `PUT /py-api/resources/{id}/status`
  - 请求参数：资源ID (路径参数), `status, reason` (可选)
  - 成功响应：`200` - 资源状态更新成功
  - 错误响应：`401` - 认证失败, `404` - 资源不存在或无权限访问, `422` - 参数验证失败, `5002` - 资源状态更新失败, `5003` - 资源状态更新失败

### **AssetController (4)**
- [ ] **161** 获取素材列表 `GET /py-api/assets/list`
  - 请求参数：`category, file_type, page, limit` (可选)
  - 成功响应：`200` - 获取素材列表成功
  - 错误响应：`5002` - 获取素材列表失败, `5003` - 获取素材列表失败

- [ ] **162** 上传素材 `POST /py-api/assets/upload`
  - 请求参数：`file_type, category, file, url, title, description` (可选)
  - 成功响应：`200` - 素材上传成功
  - 错误响应：`422` - 参数验证失败, `5002` - 素材上传失败, `5003` - 素材上传失败

- [ ] **163** 获取素材详情 `GET /py-api/assets/{id}`
  - 请求参数：素材ID (路径参数)
  - 成功响应：`200` - 获取素材详情成功
  - 错误响应：`404` - 素材不存在, `5002` - 获取素材详情失败, `5003` - 获取素材详情失败

- [ ] **164** 删除素材 `DELETE /py-api/assets/{id}`
  - 请求参数：素材ID (路径参数)
  - 成功响应：`200` - 删除素材成功
  - 错误响应：`404` - 素材不存在, `5002` - 删除素材失败, `5003` - 删除素材失败

### **PublicationController (8)**
- [ ] **165** 发布作品 `POST /py-api/publications/publish`
  - 请求参数：`resource_id, title, description, tags, category, visibility, allow_comments, allow_download` (可选)
  - 成功响应：`200` - 作品发布成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 资源不存在或未完成, `1007` - 该资源已发布或正在审核中, `5002` - 作品发布失败, `5003` - 作品发布失败

- [ ] **166** 获取发布状态 `GET /py-api/publications/{id}/status`
  - 请求参数：发布ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 发布记录不存在, `5002` - 获取发布状态失败, `5003` - 获取发布状态失败

- [ ] **167** 更新发布信息 `PUT /py-api/publications/{id}`
  - 请求参数：发布ID (路径参数), `title, description, tags, visibility, allow_comments, allow_download` (可选)
  - 成功响应：`200` - 作品信息更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 发布记录不存在, `1008` - 作品审核中，不能修改核心信息, `5002` - 更新发布信息失败, `5003` - 更新发布信息失败

- [ ] **168** 删除发布 `DELETE /py-api/publications/{id}`
  - 请求参数：发布ID (路径参数)
  - 成功响应：`200` - 作品取消发布成功
  - 错误响应：`401` - 认证失败, `404` - 发布记录不存在, `5002` - 取消发布失败, `5003` - 取消发布失败

- [ ] **169** 取消发布 `POST /py-api/publications/{id}/unpublish`
  - 请求参数：发布ID (路径参数), `reason` (可选)
  - 成功响应：`200` - 作品取消发布成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 发布记录不存在, `5002` - 取消发布失败, `5003` - 取消发布失败

- [ ] **170** 获取我的发布 `GET /py-api/publications/my-publications`
  - 请求参数：`status, category, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取发布列表失败, `5003` - 获取发布列表失败

- [ ] **171** 获取发布广场 `GET /py-api/publications/plaza`
  - 请求参数：`category, sort, tags, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`422` - 参数验证失败, `5002` - 获取作品广场失败, `5003` - 获取作品广场失败

- [ ] **172** 获取发布详情 `GET /py-api/publications/{id}/detail`
  - 请求参数：发布ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`404` - 作品不存在或不可访问, `5002` - 获取作品详情失败, `5003` - 获取作品详情失败

### **AnalyticsController (6)**
- [ ] **173** 获取用户行为分析 `GET /py-api/analytics/user-behavior`
  - 请求参数：`user_id, period` (可选)
  - 成功响应：`200` - 用户行为分析数据获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，只能分析自己的行为数据, `5002` - 用户行为分析数据获取失败, `5003` - 用户行为分析数据获取失败

- [ ] **174** 获取系统使用分析 `GET /py-api/analytics/system-usage`
  - 请求参数：`period, granularity` (可选)
  - 成功响应：`200` - 系统使用分析数据获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可查看系统统计, `5002` - 系统使用分析数据获取失败, `5003` - 系统使用分析数据获取失败

- [ ] **175** 获取AI性能分析 `GET /py-api/analytics/ai-performance`
  - 请求参数：`period, model_type, metrics` (可选)
  - 成功响应：`200` - AI性能分析数据获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可查看AI性能分析, `5002` - AI性能分析数据获取失败, `5003` - AI性能分析数据获取失败

- [ ] **176** 获取用户留存分析 `GET /py-api/analytics/user-retention`
  - 请求参数：`period, cohort_type` (可选)
  - 成功响应：`200` - 用户留存分析数据获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可查看留存分析, `5002` - 用户留存分析数据获取失败, `5003` - 用户留存分析数据获取失败

- [ ] **177** 获取收入分析 `GET /py-api/analytics/revenue`
  - 请求参数：`period, breakdown_by` (可选)
  - 成功响应：`200` - 收入分析数据获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可查看收入分析, `5002` - 收入分析数据获取失败, `5003` - 收入分析数据获取失败

- [ ] **178** 生成自定义报告 `POST /py-api/analytics/custom-report`
  - 请求参数：`report_type, parameters, format` (可选)
  - 成功响应：`200` - 自定义报告生成成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可生成自定义报告, `5002` - 自定义报告生成失败, `5003` - 自定义报告生成失败

### **BatchController (7)**
- [ ] **179** 批量生成图片 `POST /py-api/batch/images/generate`
  - 请求参数：`prompts, style, size, quality` (可选)
  - 成功响应：`200` - 批处理任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `400` - 不支持的批量任务类型, `5002` - 批处理任务创建失败, `5003` - 批处理任务创建失败

- [ ] **180** 批量合成语音 `POST /py-api/batch/voices/synthesize`
  - 请求参数：`texts, voice_id, speed, pitch` (可选)
  - 成功响应：`200` - 批处理任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `400` - 不支持的批量任务类型, `5002` - 批处理任务创建失败, `5003` - 批处理任务创建失败

- [ ] **181** 批量生成音乐 `POST /py-api/batch/music/generate`
  - 请求参数：`prompts, duration, genre` (可选)
  - 成功响应：`200` - 批处理任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `400` - 不支持的批量任务类型, `5002` - 批处理任务创建失败, `5003` - 批处理任务创建失败

- [ ] **182** 获取批量状态 `GET /py-api/batch/tasks/status`
  - 请求参数：`batch_id, task_type` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 批处理任务不存在, `403` - 无权访问此批量任务, `5002` - 批处理任务状态获取失败, `5003` - 批处理任务状态获取失败

- [ ] **183** 取消批量任务 `POST /py-api/batch/tasks/cancel`
  - 请求参数：`batch_id, reason` (可选)
  - 成功响应：`200` - 批量任务取消成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 批处理任务不存在, `403` - 无权操作此批量任务, `1008` - 批处理任务已完成或已取消, `5002` - 批处理任务取消失败, `5003` - 批处理任务取消失败

- [ ] **184** 批量生成资源 `POST /py-api/batch/resources/generate`
  - 请求参数：`resources, resource_type, batch_size` (可选)
  - 成功响应：`200` - 批量任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `400` - 不支持的批量任务类型, `5002` - 批处理任务创建失败, `5003` - 批处理任务创建失败

- [ ] **185** 获取资源状态 `GET /py-api/batch/resources/status`
  - 请求参数：`task_id` (可选)
  - 成功响应：`200` - 批处理任务结果获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 批处理任务不存在, `5002` - 批处理任务结果获取失败, `5003` - 批处理任务结果获取失败

### **WorkflowController (8)**
- [ ] **186** 创建工作流 `POST /py-api/workflows/create`
  - 请求参数：`name, description, type, steps, triggers` (可选)
  - 成功响应：`200` - 工作流创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 工作流创建失败, `5003` - 工作流创建失败

- [ ] **187** 获取工作流列表 `GET /py-api/workflows`
  - 请求参数：`status, category, page, per_page` (可选)
  - 成功响应：`200` - 工作流列表获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 工作流列表获取失败, `5003` - 工作流列表获取失败

- [ ] **188** 获取工作流详情 `GET /py-api/workflows/{id}`
  - 请求参数：工作流ID (路径参数)
  - 成功响应：`200` - 工作流详情获取成功
  - 错误响应：`401` - 认证失败, `404` - 工作流不存在, `403` - 无权限访问此工作流, `5002` - 工作流详情获取失败, `5003` - 工作流详情获取失败

- [ ] **189** 执行工作流 `POST /py-api/workflows/{id}/execute`
  - 请求参数：工作流ID (路径参数), `input_data` (可选)
  - 成功响应：`200` - 工作流执行启动成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 工作流不存在, `400` - 工作流未激活，无法执行, `5002` - 工作流执行失败, `5003` - 工作流执行失败

- [ ] **190** 获取执行状态 `GET /py-api/workflows/{id}/execution-status`
  - 请求参数：执行ID (路径参数)
  - 成功响应：`200` - 执行状态获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 执行状态获取失败, `5003` - 执行状态获取失败

- [ ] **191** 提供步骤输入 `POST /py-api/workflows/{id}/step-input`
  - 请求参数：执行ID (路径参数), `step_data` (可选)
  - 成功响应：`200` - 输入数据提交成功，工作流继续执行
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 步骤输入提交失败, `5003` - 步骤输入提交失败

- [ ] **192** 取消执行 `POST /py-api/workflows/{id}/cancel`
  - 请求参数：执行ID (路径参数), `reason` (可选)
  - 成功响应：`200` - 工作流执行取消成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 工作流执行取消失败, `5003` - 工作流执行取消失败

- [ ] **193** 获取执行历史 `GET /py-api/workflows/{id}/history`
  - 请求参数：工作流ID (路径参数), `page, per_page` (可选)
  - 成功响应：`200` - 执行历史获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 执行历史获取失败, `5003` - 执行历史获取失败

### **PermissionController (7)**
- [ ] **194** 获取用户权限 `GET /py-api/permissions/user`
  - 请求参数：`user_id` (可选)
  - 成功响应：`200` - 用户权限详情获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，只能查看自己的权限信息, `5002` - 用户权限详情获取失败, `5003` - 用户权限详情获取失败

- [ ] **195** 检查权限 `POST /py-api/permissions/check`
  - 请求参数：`permission, resource_id` (可选)
  - 成功响应：`200` - 权限验证通过
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 权限检查失败, `5003` - 权限检查失败

- [ ] **196** 获取角色列表 `GET /py-api/permissions/roles`
  - 请求参数：`page, per_page` (可选)
  - 成功响应：`200` - 角色列表获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可查看角色列表, `5002` - 角色列表获取失败, `5003` - 角色列表获取失败

- [ ] **197** 分配角色 `PUT /py-api/permissions/assign-role`
  - 请求参数：`user_id, role, reason` (可选)
  - 成功响应：`200` - 角色分配成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可分配角色, `400` - 无效的角色, `5002` - 角色分配失败, `5003` - 角色分配失败

- [ ] **198** 授予权限 `POST /py-api/permissions/grant`
  - 请求参数：`user_id, permissions, reason` (可选)
  - 成功响应：`200` - 权限授予成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可授予权限, `400` - 无效的权限, `5002` - 权限授予失败, `5003` - 权限授予失败

- [ ] **199** 撤销权限 `DELETE /py-api/permissions/revoke`
  - 请求参数：`user_id, permissions, reason` (可选)
  - 成功响应：`200` - 权限撤销成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可撤销权限, `5002` - 权限撤销失败, `5003` - 权限撤销失败

- [ ] **200** 获取权限历史 `GET /py-api/permissions/history`
  - 请求参数：`user_id, action_type, page, per_page` (可选)
  - 成功响应：`200` - 权限历史获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可查看权限历史, `5002` - 获取权限历史失败, `5003` - 获取权限历史失败

### **CacheController (8)**
- [ ] **201** 获取缓存统计 `GET /py-api/cache/stats`
  - 请求参数：无
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可查看缓存统计, `5002` - 缓存统计信息获取失败, `5003` - 缓存统计信息获取失败

- [ ] **202** 清除缓存 `DELETE /py-api/cache/clear`
  - 请求参数：`type, pattern` (可选)
  - 成功响应：`200` - 缓存清理成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可清理缓存, `5002` - 缓存清理失败, `5003` - 缓存清理失败

- [ ] **203** 预热缓存 `POST /py-api/cache/warmup`
  - 请求参数：`cache_keys, priority` (可选)
  - 成功响应：`200` - 缓存预热任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可预热缓存, `5002` - 缓存预热任务创建失败, `5003` - 缓存预热任务创建失败

- [ ] **204** 获取缓存键 `GET /py-api/cache/keys`
  - 请求参数：`pattern, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可查看缓存键, `5002` - 缓存键列表获取失败, `5003` - 缓存键列表获取失败

- [ ] **205** 获取缓存值 `GET /py-api/cache/value`
  - 请求参数：`key, format` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可查看缓存值, `404` - 缓存键不存在, `5002` - 缓存值获取失败, `5003` - 缓存值获取失败

- [ ] **206** 设置缓存值 `PUT /py-api/cache/value`
  - 请求参数：`key, value, ttl, tags` (可选)
  - 成功响应：`200` - 缓存设置成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可设置缓存值, `5002` - 缓存值设置失败, `5003` - 缓存值设置失败

- [ ] **207** 删除缓存键 `DELETE /py-api/cache/keys`
  - 请求参数：`keys` (数组)
  - 成功响应：`200` - 缓存键删除成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - 权限不足，仅管理员可删除缓存键, `5002` - 缓存键删除失败, `5003` - 缓存键删除失败

- [ ] **208** 获取缓存配置 `GET /py-api/cache/config`
  - 请求参数：无
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可查看缓存配置, `5002` - 缓存配置获取失败, `5003` - 缓存配置获取失败

### **LogController (6)**
- [ ] **209** 获取系统日志 `GET /py-api/logs/system`
  - 请求参数：`level, component, start_time, end_time, keyword, page` (可选)
  - 成功响应：`200` - 系统日志获取成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可查看系统日志, `422` - 参数验证失败, `5002` - 系统日志获取失败, `5003` - 系统日志获取失败

- [ ] **210** 获取用户行为日志 `GET /py-api/logs/user-actions`
  - 请求参数：`user_id, action, start_time, end_time, ip, page` (可选)
  - 成功响应：`200` - 用户操作日志获取成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足，只能查看自己的操作日志, `422` - 参数验证失败, `5002` - 用户操作日志获取失败, `5003` - 用户操作日志获取失败

- [ ] **211** 获取AI调用日志 `GET /py-api/logs/ai-calls`
  - 请求参数：`platform, type, status, start_time, end_time, page` (可选)
  - 成功响应：`200` - AI调用日志获取成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可查看AI调用日志, `422` - 参数验证失败, `5002` - AI调用日志获取失败, `5003` - AI调用日志获取失败

- [ ] **212** 获取错误日志 `GET /py-api/logs/errors`
  - 请求参数：`level, component, start_time, end_time, resolved, page` (可选)
  - 成功响应：`200` - 错误日志获取成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可查看错误日志, `422` - 参数验证失败, `5002` - 错误日志获取失败, `5003` - 错误日志获取失败

- [ ] **213** 解决错误 `PUT /py-api/logs/errors/{id}/resolve`
  - 请求参数：错误ID (路径参数), `solution` (可选)
  - 成功响应：`200` - 错误已标记为解决
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可解决错误, `422` - 参数验证失败, `5002` - 错误解决失败, `5003` - 错误解决失败

- [ ] **214** 导出日志 `POST /py-api/logs/export`
  - 请求参数：`type, format, filters` (可选)
  - 成功响应：`200` - 日志导出任务创建成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可导出日志, `422` - 参数验证失败, `5002` - 日志导出失败, `5003` - 日志导出失败

### **AdController (2)**
- [ ] **215** 广告开始 `POST /py-api/ad.store`
  - 请求参数：`ad_id`
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 广告记录保存失败, `5003` - 广告记录保存失败

- [ ] **216** 广告更新 `POST /py-api/ad.update`
  - 请求参数：`id, time_length, state`
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 广告记录更新失败, `5003` - 广告记录更新失败

### **DownloadController (7)**
- [ ] **217** 获取下载列表 `GET /py-api/downloads/list`
  - 请求参数：`download_type, status, date_from, date_to, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取下载历史失败, `5003` - 获取下载历史失败

- [ ] **218** 重试下载 `POST /py-api/downloads/{id}/retry`
  - 请求参数：下载ID (路径参数)
  - 成功响应：`200` - 下载重试成功
  - 错误响应：`401` - 认证失败, `404` - 下载记录不存在, `404` - 目标资源不再可用, `1007` - 只能重试失败的下载任务, `1003` - 重试次数已达上限, `5002` - 下载重试失败, `5003` - 下载重试失败

- [ ] **219** 获取下载统计 `GET /py-api/downloads/statistics`
  - 请求参数：`period, download_type` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取下载统计失败, `5003` - 获取下载统计失败

- [ ] **220** 创建下载链接 `POST /py-api/downloads/create-link`
  - 请求参数：`target_type, target_id, expires_in, download_name` (可选)
  - 成功响应：`200` - 下载链接创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 目标资源不存在或无权限访问, `5002` - 创建下载链接失败, `5003` - 创建下载链接失败

- [ ] **221** 安全下载 `GET /py-api/downloads/secure/{token}`
  - 请求参数：下载令牌 (路径参数)
  - 成功响应：`200` - 文件下载成功
  - 错误响应：`401` - 下载令牌无效或已过期, `404` - 下载记录不存在, `404` - 文件不存在, `400` - 下载链接不可用, `410` - 下载链接已过期, `5002` - 下载失败, `5003` - 下载失败

- [ ] **222** 批量下载 `POST /py-api/downloads/batch`
  - 请求参数：`items, archive_name, compression_level` (可选)
  - 成功响应：`200` - 批量下载任务创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 资源不存在或无权限访问, `5002` - 创建批量下载失败, `5003` - 创建批量下载失败

- [ ] **223** 清理下载 `POST /py-api/downloads/cleanup`
  - 请求参数：`days_old, cleanup_files` (可选)
  - 成功响应：`200` - 下载记录清理完成
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 清理下载记录失败, `5003` - 清理下载记录失败

### **ReviewController (7)**
- [ ] **224** 提交审核 `POST /py-api/reviews/submit`
  - 请求参数：`publication_id, review_type, additional_info` (可选)
  - 成功响应：`200` - 审核提交成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 发布记录不存在, `1008` - 该作品已在审核队列中, `5002` - 审核提交失败, `5003` - 审核提交失败

- [ ] **225** 获取审核状态 `GET /py-api/reviews/{id}/status`
  - 请求参数：审核ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 审核记录不存在, `5002` - 获取审核状态失败, `5003` - 获取审核状态失败

- [ ] **226** 申诉审核 `POST /py-api/reviews/{id}/appeal`
  - 请求参数：审核ID (路径参数), `appeal_reason, additional_evidence` (可选)
  - 成功响应：`200` - 申诉提交成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 审核记录不存在, `1007` - 只能对被拒绝的审核结果提出申诉, `1002` - 申诉期限已过, `1003` - 申诉次数已达上限, `5002` - 申诉提交失败, `5003` - 申诉提交失败

- [ ] **227** 获取我的审核 `GET /py-api/reviews/my-reviews`
  - 请求参数：`status, review_type, date_from, date_to, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取审核记录失败, `5003` - 获取审核记录失败

- [ ] **228** 获取队列状态 `GET /py-api/reviews/queue-status`
  - 请求参数：无
  - 成功响应：`200` - success
  - 错误响应：`5002` - 获取队列状态失败, `5003` - 获取队列状态失败

- [ ] **229** 获取审核指南 `GET /py-api/reviews/guidelines`
  - 请求参数：无
  - 成功响应：`200` - success
  - 错误响应：`5002` - 获取审核指南失败, `5003` - 获取审核指南失败

- [ ] **230** 预检查 `POST /py-api/reviews/pre-check`
  - 请求参数：`resource_id, title, description, tags` (可选)
  - 成功响应：`200` - 预检完成
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 预检失败, `5003` - 预检失败

### **SocialController (9)**
- [ ] **231** 关注/取消关注 `POST /py-api/social/follow`
  - 请求参数：`target_user_id, action`
  - 成功响应：`200` - 关注操作成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `400` - 不能关注自己, `1008` - 已经关注该用户, `404` - 未关注该用户, `5002` - 关注操作失败, `5003` - 关注操作失败

- [ ] **232** 获取关注列表 `GET /py-api/social/follows`
  - 请求参数：`user_id, type, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`422` - 参数验证失败, `5002` - 获取关注列表失败, `5003` - 获取关注列表失败

- [ ] **233** 点赞/取消点赞 `POST /py-api/social/like`
  - 请求参数：`target_type, target_id, action`
  - 成功响应：`200` - 点赞操作成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 目标内容不存在, `1008` - 已经点赞过了, `404` - 未点赞过该内容, `5002` - 点赞操作失败, `5003` - 点赞操作失败

- [ ] **234** 评论 `POST /py-api/social/comment`
  - 请求参数：`target_type, target_id, content, parent_id` (可选)
  - 成功响应：`200` - 评论发布成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 目标内容不存在, `400` - 父评论不存在或不匹配, `5002` - 评论发布失败, `5003` - 评论发布失败

- [ ] **235** 获取评论列表 `GET /py-api/social/{id}/comments`
  - 请求参数：目标ID (路径参数), `target_type, sort, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`422` - 参数验证失败, `404` - 目标内容不存在, `5002` - 获取评论列表失败, `5003` - 获取评论列表失败

- [ ] **236** 分享 `POST /py-api/social/share`
  - 请求参数：`target_type, target_id, platform, message` (可选)
  - 成功响应：`200` - 分享成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 分享目标不存在, `5002` - 分享失败, `5003` - 分享失败

- [ ] **237** 获取动态流 `GET /py-api/social/feed`
  - 请求参数：`type, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取社交动态失败, `5003` - 获取社交动态失败

- [ ] **238** 获取社交通知 `GET /py-api/social/notifications`
  - 请求参数：`type, status, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取通知失败, `5003` - 获取通知失败

- [ ] **239** 标记通知已读 `PUT /py-api/social/notifications/read`
  - 请求参数：`notification_ids` (可选)
  - 成功响应：`200` - 通知标记成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 标记通知失败, `5003` - 标记通知失败

### **TaskManagementController (5)**
- [ ] **240** 取消任务 `POST /py-api/tasks/{id}/cancel`
  - 请求参数：任务ID (路径参数), `reason` (可选)
  - 成功响应：`200` - 任务已取消
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `400` - 任务ID无效, `404` - 任务不存在, `403` - 用户无权取消该任务, `409` - 任务已完成或已取消, `5002` - 任务取消失败, `5003` - 任务取消失败

- [ ] **241** 重试任务 `POST /py-api/tasks/{id}/retry`
  - 请求参数：任务ID (路径参数), `platform` (可选)
  - 成功响应：`200` - 任务重试成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 任务不存在, `403` - 用户无权重试该任务, `409` - 任务状态不允许重试, `400` - 任务重试次数已达上限, `5002` - 任务重试失败, `5003` - 任务重试失败

- [ ] **242** 获取批量状态 `GET /py-api/tasks/batch/status`
  - 请求参数：`task_ids` (string, 任务ID列表), `batch_id` (string, 批量任务ID)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `5002` - 获取批量状态失败, `5003` - 获取批量状态失败

- [ ] **243** 获取超时配置 `GET /py-api/tasks/timeout-config`
  - 请求参数：无
  - 成功响应：`200` - success
  - 错误响应：`400` - 获取超时配置失败, `5002` - 获取超时配置失败, `5003` - 获取超时配置失败

- [ ] **244** 查询恢复状态 `GET /py-api/tasks/{id}/recovery-status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `403` - 权限不足, `400` - 查询恢复状态失败, `5002` - 查询恢复状态失败, `5003` - 查询恢复状态失败

### **VersionController (6)**
- [ ] **245** 创建版本 `POST /py-api/resources/{id}/versions`
  - 请求参数：资源ID (路径参数), `version_name, description, generation_config, base_version_id` (可选)
  - 成功响应：`200` - 版本创建成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 资源不存在或无权限访问, `1006` - 积分不足, `5002` - 版本创建失败, `5003` - 版本创建失败

- [ ] **246** 获取版本列表 `GET /py-api/resources/{id}/versions`
  - 请求参数：资源ID (路径参数), `page, per_page, status` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 资源不存在或无权限访问, `5002` - 获取版本历史失败, `5003` - 获取版本历史失败

- [ ] **247** 获取版本详情 `GET /py-api/versions/{id}`
  - 请求参数：版本ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 版本不存在或无权限访问, `5002` - 获取版本详情失败, `5003` - 获取版本详情失败

- [ ] **248** 设置当前版本 `PUT /py-api/versions/{id}/set-current`
  - 请求参数：版本ID (路径参数)
  - 成功响应：`200` - 当前版本设置成功
  - 错误响应：`401` - 认证失败, `404` - 版本不存在或无权限访问, `1007` - 只能设置已完成的版本为当前版本, `5002` - 设置当前版本失败, `5003` - 设置当前版本失败

- [ ] **249** 删除版本 `DELETE /py-api/versions/{id}`
  - 请求参数：版本ID (路径参数)
  - 成功响应：`200` - 版本删除成功
  - 错误响应：`401` - 认证失败, `404` - 版本不存在或无权限访问, `1007` - 不能删除当前版本, `5002` - 版本删除失败, `5003` - 版本删除失败

- [ ] **250** 比较版本 `GET /py-api/versions/compare`
  - 请求参数：`version1_id, version2_id`
  - 成功响应：`200` - 版本比较完成
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 版本不存在或无权限访问, `400` - 只能比较同一资源的不同版本, `5002` - 版本比较失败, `5003` - 版本比较失败

### **WebSocketController (4)**
- [ ] **251** WebSocket认证 `POST /py-api/websocket/auth`
  - 请求参数：`client_type, client_info` (可选)
  - 成功响应：`200` - 认证成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `403` - WEB工具禁用WebSocket连接，请使用HTTP API, `400` - 无效的客户端类型, `1000` - 用户连接数已达上限, `5002` - 连接认证失败, `5003` - 连接认证失败

- [ ] **252** 获取WebSocket会话 `GET /py-api/websocket/sessions`
  - 请求参数：`status, client_type` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `5002` - 获取会话列表失败, `5003` - 获取会话列表失败

- [ ] **253** 断开WebSocket连接 `POST /py-api/websocket/disconnect`
  - 请求参数：`session_id, reason` (可选)
  - 成功响应：`200` - 连接已断开
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 会话不存在, `1007` - 会话已断开, `5002` - 断开连接失败, `5003` - 断开连接失败

- [ ] **254** 获取WebSocket状态 `GET /py-api/websocket/status`
  - 请求参数：无
  - 成功响应：`200` - success
  - 错误响应：`5002` - 获取服务器状态失败, `5003` - 获取服务器状态失败

### **WorkPublishController (8)**
- [ ] **255** 发布作品 `POST /py-api/works/publish`
  - 请求参数：`title, description, file_path, resource_id, status` (可选)
  - 成功响应：`200` - 作品发布成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `422` - 资源ID不存在或未完成, `1008` - 该资源已发布或正在审核中, `403` - 发布权限不足, `5002` - 作品发布失败, `5003` - 作品发布失败

- [ ] **256** 更新作品 `PUT /py-api/works/{id}`
  - 请求参数：作品ID (路径参数), `title, description, tags` (可选)
  - 成功响应：`200` - 作品更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `404` - 作品不存在或无权限, `1007` - 作品已发布且正在人工审核中，不能修改, `5002` - 作品更新失败, `5003` - 作品更新失败

- [ ] **257** 删除作品 `DELETE /py-api/works/{id}`
  - 请求参数：作品ID (路径参数)
  - 成功响应：`200` - 作品删除成功
  - 错误响应：`401` - 认证失败, `404` - 作品不存在或无权限, `5002` - 作品删除失败, `5003` - 作品删除失败

- [ ] **258** 获取我的作品 `GET /py-api/works/my-works`
  - 请求参数：`work_type, publish_status, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败, `5002` - 获取失败, `5003` - 获取失败

- [ ] **259** 作品展示库 `GET /py-api/works/gallery`
  - 请求参数：`work_type, featured, sort_by, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`5002` - 获取失败, `5003` - 获取失败

- [ ] **260** 获取分享链接 `GET /py-api/works/{id}/share-link`
  - 请求参数：作品ID (路径参数), `type, password, expires_at, max_access_count` (可选)
  - 成功响应：`200` - 分享链接生成成功
  - 错误响应：`401` - 认证失败, `404` - 作品不存在或无权限, `5002` - 生成分享链接失败, `5003` - 生成分享链接失败

- [ ] **261** 点赞作品 `POST /py-api/works/{id}/like`
  - 请求参数：作品ID (路径参数)
  - 成功响应：`200` - 点赞成功/取消点赞成功
  - 错误响应：`401` - 认证失败, `404` - 作品不存在或未发布, `5002` - 点赞操作失败, `5003` - 点赞操作失败

- [ ] **262** 热门作品 `GET /py-api/works/trending`
  - 请求参数：`work_type, period, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`5002` - 获取失败, `5003` - 获取失败